import {
  Injectable,
  CanActivate,
  ExecutionContext,
  TooManyRequestsException,
} from '@nestjs/common';
import { Request } from 'express';

/**
 * Upload Rate Limiting Guard
 * Prevents abuse by limiting upload requests per IP address
 */
@Injectable()
export class UploadRateLimitGuard implements CanActivate {
  private readonly uploadAttempts = new Map<
    string,
    { count: number; resetTime: number }
  >();
  private readonly MAX_UPLOADS_PER_MINUTE = 10;
  private readonly WINDOW_MS = 60 * 1000;

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const clientIp = this.getClientIp(request);

    const now = Date.now();
    const userAttempts = this.uploadAttempts.get(clientIp);

    if (!userAttempts || now > userAttempts.resetTime) {
      this.uploadAttempts.set(clientIp, {
        count: 1,
        resetTime: now + this.WINDOW_MS,
      });
      return true;
    }

    if (userAttempts.count >= this.MAX_UPLOADS_PER_MINUTE) {
      throw new TooManyRequestsException(
        `Too many upload attempts. Please wait ${Math.ceil((userAttempts.resetTime - now) / 1000)} seconds.`,
      );
    }

    userAttempts.count++;
    return true;
  }

  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string) ||
      (request.headers['x-real-ip'] as string) ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}
