/**
 * Upload configuration constants for DoS protection and file handling
 *
 * These limits are designed to prevent Denial of Service attacks while
 * allowing reasonable file uploads for the application.
 */

export const UPLOAD_LIMITS = {
  // File size limits (in bytes)
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB - Global maximum for DoS protection
  IMAGE_MAX_SIZE: 2 * 1024 * 1024, // 2MB - For image uploads
  DOCUMENT_MAX_SIZE: 5 * 1024 * 1024, // 5MB - For documents (CSV, PDF, DOCX, PPTX)

  // Request limits
  MAX_FILES_PER_REQUEST: 5, // Maximum files per upload request
  MAX_FORM_FIELDS: 10, // Maximum form fields per request
  MAX_FIELD_NAME_SIZE: 100, // Maximum field name size (bytes)
  MAX_FIELD_VALUE_SIZE: 1024 * 1024, // Maximum field value size (1MB)

  // Rate limiting (for additional protection)
  UPLOAD_RATE_LIMIT: {
    WINDOW_MS: 60 * 1000, // 1 minute window
    MAX_REQUESTS: 5, // Maximum 5 upload requests per minute per user
  },
} as const;

/**
 * Allowed file types for different upload contexts
 */
export const ALLOWED_FILE_TYPES = {
  IMAGES: ['image/png', 'image/jpeg', 'image/jpg'],
  DOCUMENTS: [
    'text/csv',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
  ],
  ALL: [] as string[], // Will be populated below
} as const;

// Populate ALL with combined types
ALLOWED_FILE_TYPES.ALL = [
  ...ALLOWED_FILE_TYPES.IMAGES,
  ...ALLOWED_FILE_TYPES.DOCUMENTS,
];

/**
 * File type validation patterns for different contexts
 */
export const FILE_TYPE_PATTERNS = {
  IMAGES: /\.(png|jpeg|jpg)$/i,
  DOCUMENTS: /\.(csv|pdf|docx|pptx)$/i,
  ALL: /\.(png|jpeg|jpg|csv|pdf|docx|pptx)$/i,
} as const;

/**
 * Error messages for upload validation
 */
export const UPLOAD_ERROR_MESSAGES = {
  FILE_TOO_LARGE: (maxSize: number) =>
    `File is too large. Maximum size is ${maxSize / (1024 * 1024)}MB`,
  INVALID_FILE_TYPE:
    'Invalid file type. Please upload a supported file format.',
  TOO_MANY_FILES: (maxFiles: number) =>
    `Too many files. Maximum ${maxFiles} files allowed per request.`,
  EMPTY_FILE: 'Empty file is not allowed.',
  UPLOAD_FAILED: 'File upload failed. Please try again.',
} as const;
